{"name": "console-palo", "version": "1.0.0", "description": "百度智能云Palo", "main": "bootstrap.ts", "scripts": {"dev": "NODE_OPTIONS=--openssl-legacy-provider bce-cli dev --template=public-console --config=bce-config.js", "dev:xs-console": "bce-cli dev --template=xs-console --config=bce-config.js", "build-local": "bce-cli build --template=public-console --config=bce-config.js --no-i18n", "build": "NODE_OPTIONS=--openssl-legacy-provider bce-cli build --template=public-console --config=bce-config.js", "build:xs-console": "NODE_OPTIONS=--openssl-legacy-provider bce-cli build --template=xs-console --config=bce-config.js --no-i18n"}, "author": "author", "dependencies": {"@baidu/bce-billing-sdk": "^2.1.4", "@baidu/bce-billing-sdk-san": "^2.1.3", "@baidu/bce-httpclient": "1.0.0-beta.3", "@baidu/bce-template": "^1.0.15", "@baidu/enum": "^0.0.5", "@baidu/sui-icon": "^1.0.41", "@baidu/xicon-san": "^0.0.43", "@baiducloud/i18n": "^1.0.0-rc.29", "@baiducloud/runtime": "^1.0.0-rc.38", "file-loader": "^6.2.0", "file-size": "^1.0.0", "jsencrypt": "^3.5.4", "moment": "^2.30.1", "resize-observer-polyfill": "^1.5.1", "san": "3.7.9", "url-loader": "^4.1.1"}, "devDependencies": {"@babel/plugin-proposal-optional-chaining": "^7.20.7", "@baidu/bce-cli": "^1.1.6-beta.6", "@types/lodash": "^3.10.3", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^9.8.0", "cssnano": "^4.1.10", "eslint": "^8.31.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.1.1", "mini-css-extract-plugin": "^1.3.0", "postcss": "^8.5.3", "postcss-less": "^3.1.4", "postcss-loader": "^3.0.0", "prettier": "^3.3.3", "prettier-eslint": "^16.3.0", "stylelint": "^14.16.1", "stylelint-config-standard-less": "^3.0.1", "typescript": "^4.9.5"}}