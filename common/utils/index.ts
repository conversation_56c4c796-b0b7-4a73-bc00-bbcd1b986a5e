import m from 'moment';
import _ from 'lodash';
import filesize from 'file-size';
import {backupStatus} from "@common/config/constant";
import {clusterStatus} from '@common/config';
import {JSEncrypt} from 'jsencrypt';

export const formateSize = (size: number = 0) => {
    return filesize(size).human('jedec');
};

export function getDeployId() {
    return location.hash.split('=')[1];
};

export function getRealTime(time: string | number | Date) {
    let arr = [];
    function addZero(num: string | number) {
        if (num < 10) {
            return '0' + num;
        }
        return num;
    }
    let dar = new Date(time);
    // 按自定义拼接格式返回
    return dar.getFullYear() + '-' + addZero(dar.getMonth() + 1) + '-' + addZero(dar.getDate()) + ' '
        + addZero(dar.getHours()) + ':' + addZero(dar.getMinutes()) + ':' + addZero(dar.getSeconds());
};

export function getAddTime(time1: any, time2: any) {
    let arr1 = time1.split(' ');
    let arr2 = arr1[0].split('-');
    let [year, month] = arr2;
    if (time2 < 12) {
        month = + month + time2;
        if (month > 12) {
            year = + year + Math.floor(month / 12);
            month = month % 12;
        }
    };
    if (time2 >= 12) {
        year = + year + time2 / 12;
    };
    arr2.splice(0, 2, year, month);
    return arr2.join('-') + ' ' + arr1[1];
}

export function getSelection(arr: any, obj: any) {
    let local = JSON.parse(JSON.stringify(obj));
    for (let item in local) {
        if (arr.indexOf(local[item].name) === -1) {
            delete local[item];
        }
    }
    return local;
};

// 空文本
export const formatEmpty = (value?: string | number) => !value ? '--' : value;

export const renderStatus = (displayStatus: string, status: string) =>
    `<span class="status ${clusterStatus[status]}">${displayStatus}</span>`;

export const formateBackupStatus = (displayStatus: string, status: string) => {
    return `<span class="status ${backupStatus[status]}">${displayStatus}</span>`;
}

export const renderSwitch = (bool: boolean) =>
    bool ? '开启' : '关闭';

// 格式化时间
export const formatTime = (time?: string | number | null) => {
    return time ? m(time).format('YYYY-MM-DD HH:mm:ss') : formatEmpty();
};

// 将时间转化为utc格式
export const formatUtcTime = (time: string | number, pattern?: string) => {
    return m(time).utc().format(pattern || 'YYYY-MM-DDTHH:mm:ss') + 'Z';
};

export const getTimeStamp = (time?: string) => {
    return time ? m(time).valueOf() : time;
};

// 将url参数处理为对象{key: value}
// eg: xx?a=1&b=2 ——》{a: 1, b: 2}
export const formatUrlQuery = hash => {
    let arr = hash.split('?');
    if (!arr[1]) {
        return {};
    }

    let res = arr[1].split('&');
    let params = {};
    for (let i = 0; i < res.length; i++) {
        const a = res[i].split('=');
        params[a[0]] = a[1];
    }
    return params;
};

const serializeQueryFun = (query, encodeFun = param => param) => {
    if (!query || _.isEmpty(query)) {
        return '';
    }
    let search = '';
    for (const key in query) {
        if (query.hasOwnProperty(key)) {
            const value = query[key];
            if (value === undefined || value === null) {
                continue;
            }
            // 如果`value`是数组，其`toString`会自动转为逗号分隔的字符串
            search += '&' + encodeFun(key) + '=' + encodeFun(value.toString());
        }
    }
    return search.slice(1);
};

// 系列化 query 对象 为字符串，使用场景为：对URL处理 【如对接口参数请使用 serializeQueryEncode】
// 例如： {a: 1, b: 2} 转为 a=1&b=2
export const serializeQuery = query => serializeQueryFun(query);
// 系列化 query 对象 为字符串，使用场景为：对接口参数  【如针对URL处理请使用 serializeQuery】
// 例如： {a: %} 转为 a=%25
export const serializeQueryEncode = query => serializeQueryFun(query, encodeURIComponent);

/**
 * // 更新 url 中参数
 * @param {*} params query 对象
 */
export const updateUrlQuery = params => {
    const href = location.href;
    let prevParams = formatUrlQuery(location.hash);
    const splitArr = href.split('?');
    // ['https://console.bce.baidu.com/bmr/', '_=111#/bmr/cluster/detail', 'clusterId=222']
    let newUrl = splitArr[0] || '';
    if (splitArr[1] && splitArr[1].includes('_=')) {
        newUrl += '?' + splitArr[1];
    }
    newUrl += '?' + serializeQuery({
        ...prevParams,
        ...params
    });
    window.history.replaceState('', '', newUrl);
};

/**
 * // 删除 url 中参数
 * @param {*} params query 对象
 */
export const deleteUrlQuery = params => {
    const href = location.href;
    let prevParams = formatUrlQuery(location.hash);
    const splitArr = href.split('?');
    let newUrl = splitArr[0] || '';
    if (splitArr[1] && splitArr[1].includes('_=')) {
        newUrl += '?' + splitArr[1];
    }
    newUrl += '?' + serializeQuery({
        ..._.omit(prevParams, params)
    });
    window.history.replaceState('', '', newUrl);
};

export const computeTimeDistance = (timestamp2, timestamp1) => {
    if (!timestamp2 || !timestamp1) return '--';

    // 计算时间差（以毫秒为单位）
    const diff = Math.abs(timestamp1 - timestamp2);

    // 将时间差转换为秒
    let totalSeconds = Math.floor(diff / 1000);

    // 计算天数、小时数、分钟数和秒数
    const days = Math.floor(totalSeconds / (24 * 3600));
    totalSeconds %= 24 * 3600;
    const hours = Math.floor(totalSeconds / 3600);
    totalSeconds %= 3600;
    const minutes = Math.floor(totalSeconds / 60);

    // 格式化输出
    let result = '';
    if (days > 0) {
        result += `${days}天`;
    }
    if (hours > 0) {
        result += `${hours}小时`;
    }
    result += `${minutes}分钟`;

    return result;
}

export const pickEmpty = (obj: Object) => {
    // @ts-ignore
    return _.pick(obj, _.identity);
};

// 自定义 identity 函数
export const customIdentity = (value, key) => {
    // 如果属性名是 isQuery 且值为 0，则认为是符合条件的
    if (key === 'isQuery' && value === 0) {
        return true;
    }
    // 默认逻辑：保留 truthy 值
    return !!value;
};

export const compareVersions = (version1: string = '', version2: string = ''): number => {
    const v1 = version1?.split('.')?.map(num => parseInt(num, 10));
    const v2 = version2?.split('.')?.map(num => parseInt(num, 10));
    // 补齐较短的版本号
    while (v1?.length < v2?.length) v1.push(0);
    while (v2?.length < v1?.length) v2.push(0);

    // 比较版本号的每一部分
    for (let i = 0; i < v1?.length; i++) {
        if (v1[i] < v2[i]) {
            return -1;
        };  // version1 < version2
        if (v1[i] > v2[i]) {
            return 1;
        };   // version1 > version2
    }

    return 0;  // 两个版本号相等
}

/**
* 忽略大小写判断两个字符串是否相等
* @param {string} a
* @param {string} b
* @returns {boolean}
*/
export const equalsIgnoreCase = (a: string, b: string) => {
    // 强制转成字符串并统一为小写
    return String(a).toLowerCase() === String(b).toLowerCase();
}

/**
 * 判断是否已到期
 * @param {number|string} expiryTimestamp - 到期时间戳（毫秒）
 * @returns {boolean} - 如果已到期则返回 true，否则返回 false
 */
export const isExpired = (expiryTimestamp: string | number): boolean => {
    // 确保是数字
    const expireTime = typeof expiryTimestamp === 'string'
        ? parseInt(expiryTimestamp, 10)
        : expiryTimestamp;

    // 当前时间毫秒
    const now = Date.now();

    // 比较
    return now >= expireTime;
}

export const daysUntil = (targetISOString: Date) => {
    const now = new Date();
    const targetDate = new Date(targetISOString);

    const diffMs = targetDate.getTime() - now.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    return diffDays + '天';
}

/**
* 使用公钥加密密码
*
* @param encryptInfo 包含密码的对象
* @returns 加密后的密码
*/
export const passwordCrypt = (encryptInfo: any) => {
    const crypt = new JSEncrypt();
    // For encryption only (using public key)
    const publicKeyString = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDDHay39x4ret1XzrxnJ6QBCl7e/GDWIcQCSE+y+Tbvn/WTNuDZQCN+ujVuFy5rBDkB7IQ3iD8rq1qOLJ+sLoA9uLa6haMUIFhbCkFSrXXzlVlb7sLd4NiNKCS/i/8IoxtmT3FHtJvfAq2+U/GXUfqtImQiK4NExZfDydVMcPwsWwIDAQAB";
    crypt.setPublicKey(encryptInfo?.publicKeyString || publicKeyString);
    const encrypted: any = crypt.encrypt(encryptInfo.cryptCont);
    return encrypted;
};

/**
* 将字符串编码为Base64格式
*
* @param str 待编码的字符串
* @returns 返回编码后的Base64字符串
*/
export const base64Encode = (str: string) => {
    const encoder = new TextEncoder();
    const uint8Array: any = encoder.encode(str);
    const base64String = btoa(String.fromCharCode.apply(null, uint8Array));
    return base64String;
}