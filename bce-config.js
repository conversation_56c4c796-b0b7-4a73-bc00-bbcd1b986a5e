/**
 * @file 工具模块启动配置
 */
const path = require('path');
const webpack = require('webpack');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const ENVIRONMENT = process.env.npm_lifecycle_event;
// 公有云
const isRunPublic = ENVIRONMENT === 'build' || ENVIRONMENT === 'dev';
const isRunXuShang = ENVIRONMENT === 'build:xs-console' || ENVIRONMENT === 'dev:xs-console';
let envExtraDependencies = {};
let envExtraEslMap = {};
const WebpackTimeId = Date.now();
const isDev = process.env.npm_lifecycle_event === 'dev';
const publicPath = 'https://bce.bdstatic.com/console/static/palo/';


// 1.公有云/虚商项目侧bui采用online-config.js/qasandbox-config.js中的bui，
// 注意：
// （1）公有云/虚商项目的bui版本不能与online-config/qasandbox-config中的bui版本一样，会导致页面白屏
// 具体原因见文档中的三.2.5：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/qMApQ04r-e/c69Mh3zXaa/cqbszr7a2EWht0#anchor-e94de980-4641-11ee-9591-8531730df0ca
// （2）不能再自定义其它bui版本，否则会加载两个不同版本bui，原因见如上文档
// if (!isRunPublic && !isRunXuShang) {
//     // 非公有云&非虚商自定义bui
//     envExtraDependencies = {
//         '@baiducloud/bce-ui/san': '@baiducloud/ui/********/bui-san.bundles.js',
//     };
//     envExtraEslMap = {
//         // 确保其它模块依赖的@baiducloud/bce-ui/san能找到
//         '@baiducloud/bce-ui/san': '@baiducloud/bce-ui/san@********',
//     };
// }


module.exports = {
    // 模块相关配置
    service: 'PALO',
    pathname: 'palo',
    flags: ['PALO'],

    runtime: '@baiducloud/runtime',

    // mock配置
    mockup: {
        caching: true,
        rules: ['/api/palo/(.*)'], // 默认值 /api/pathname/*
        root: '.mockup'
    },

    // debug调试
    debug: {
        // '@baiducloud/runtime': 'http://localhost:8989/runtime.js',
        // '@baiducloud/xxx-sdk': 'http://localhost:8990/xxx-sdk.js'
    },

    // 代理地址
    // proxyTarget: 'https://qasandbox.bcetest.baidu.com',
    proxyTarget: 'https://console.bce.baidu.com',

    // 需要排除的依赖，模块会帮助配置esl，sdk则需要依赖模块的配置
    dependencies: {
        '@baidu/bce-bcm-sdk-san': '@baiducloud/fe-bcm-sdk/********/bcm-sdk-san.js',
        '@baidu/bce-bcm-sdk': '@baiducloud/fe-bcm-sdk/********/bcm-sdk.js',
        '@baidu/bce-tag-sdk': '@baiducloud/fe-tag-sdk/********/tag-sdk.js',
        '@baiducloud/tag-sdk/san': '@baiducloud/fe-tag-sdk/********/tag-sdk-san.js',
        '@baidu/sui': '@baiducloud/sui/1.0.337.1/sui.js',
        '@baidu/sui-biz': '@baiducloud/sui-biz/********/sui-biz.js',
        '@baiducloud/billing-sdk': '@baiducloud/fe-billing-sdk/********/billing-sdk.js',
        '@baiducloud/billing-sdk/san': '@baiducloud/fe-billing-sdk/********/billing-sdk-san.js',
        // ...envExtraDependencies
    },

    eslConfigAddMap: {
        '@baidu/sui': '@baidu/sui@1.0.337.1', // 必须要加，方便UMD模块的 sui-biz 对于 @baidu/sui 的依赖能找到
        // 根据项目运行环境设置额外的eslMap
        // ...envExtraEslMap
    },

    // 自定义webpack，可选，默认一个入口 index.js/index.ts
    webpack: {
        resolve: {
            extensions: ['.ts', '.js'],
            alias: {
                '@/': path.resolve(__dirname, './'),
                '@common': path.resolve(__dirname, './common'),
                '@components': path.resolve(__dirname, './components'),
                '@pages': path.resolve(__dirname, './pages'),
                '@style': path.resolve(__dirname, './style'),
                '@static': path.resolve(__dirname, './static'),
            }
        },
        output: {
            publicPath: isDev ? '' : publicPath
        },
        entry: {},
        externals: [
            '@baiducloud/httpclient',
            '@baiducloud/i18n',
            '@baiducloud/runtime',
            '@baiducloud/tag-sdk/san',
            '@baidu/bce-bcm-sdk-san',
            '@baidu/bce-bcm-sdk',
            '@baidu/bce-tag-sdk',
            '@baidu/sui',
            '@baidu/sui-biz',
            '@baiducloud/billing-sdk',
            '@baiducloud/billing-sdk/san',
        ],
        plugins: [
            new MiniCssExtractPlugin({
                filename: `bootstrap.${WebpackTimeId}.css`, // CSS 文件带 hash
            }),
            new webpack.DefinePlugin({
                // 插入 hash 值到 JS 文件中
                __WEBPACK_TIME_ID__: WebpackTimeId,
                'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'production'),
            })
        ],
        module: {
            rules: [
              {
                test: /\.ts$/,
                exclude: /node_modules/,
                      use: {
                          loader: 'babel-loader',
                          options: {
                            presets: ['@babel/preset-env', '@babel/preset-typescript'],
                            plugins: [['@babel/plugin-proposal-decorators', {legacy: true}]]
                          },
                      },
                },
                {
                    test: /\.(css|less)$/,
                    use: [
                        {
                            loader: MiniCssExtractPlugin.loader,
                            options: {esModule: false}
                        },
                        {
                            loader: 'css-loader',
                            options: {
                                sourceMap: false
                            }
                        },
                        {
                            loader: 'postcss-loader',
                            options: {
                                sourceMap: false,
                                plugins: loader => [
                                    require('autoprefixer')(),
                                    require('cssnano')({preset: 'default'})
                                ],
                            },
                        },
                        {
                            loader: 'less-loader',
                            options: {
                                sourceMap: false,
                            }
                        }
                    ]
                },
                {
                    test: /\.(?:ico|gif|png|jpg|jpeg|webp)$/,
                    use: {
                        loader: 'url-loader',
                        options: {
                            limit: 5120, // 小于等于5KB的图片，才会用转换成base64
                            name: '[name].[contenthash:8].[ext]',
                            esModule: false
                        }
                    }
                },
                {
                    test: /\.svg(\?v=\d+\.\d+\.\d+)?$/,
                    use: {
                        loader: 'url-loader',
                        options: {
                            limit: 5120, // 小于等于5KB的SVG，才会用转换成base64
                            mimetype: 'image/svg+xml',
                            name: '[name].[contenthash:8].[ext]',
                            esModule: false
                        }
                    }
                }
            ]
        },
        optimization: {}
    }
};
