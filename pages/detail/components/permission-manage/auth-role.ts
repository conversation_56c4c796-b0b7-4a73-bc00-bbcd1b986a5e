import {Component} from 'san';
import {debounce} from '@common/decorators';
import {html, redirect} from '@baiducloud/runtime';
import {Table, Button, Search, Pagination, Notification, Dialog, Tooltip} from '@baidu/sui';
import {Plus1} from '@baidu/xicon-san';
import EllipsisTip from '@components/ellipsis-tip';
import './index.less';
import RoleDialog from './dialogs/role-dialog';
import { serializeQueryEncode } from '@common/utils';

enum roleOrigin {
  SYSTEM = 'SYSTEM',
  USER = 'USER'
}

const originInfo = {
  [roleOrigin.SYSTEM]: '系统',
  [roleOrigin.USER]: '用户'
}

const roleColumns = [
  {
    name: 'name',
    label: '角色名称',
  },
  {
    name: 'origin',
    label: '来源',
    width: 100,
    filter: {
      options: [
        {text: '全部', value: ''},
        {text: originInfo[roleOrigin.SYSTEM], value: roleOrigin.SYSTEM},
        {text: originInfo[roleOrigin.USER], value: roleOrigin.USER}
      ],
      value: ''
    },
    render: (item: {origin: string}) => {
      if (!item.origin) return '-';
      return item.origin === roleOrigin.SYSTEM ? originInfo[roleOrigin.SYSTEM] : originInfo[roleOrigin.USER];
    }
  },
  {
    name: 'users',
    label: '授予用户',
  },
  {
    name: 'createTime',
    label: '创建时间',
    width: 150,
    render: (item: {createTime: string}) => {
      return item.createTime || '-';
    },
  },
  {
    name: 'comment',
    label: '描述',
  },
  {
    name: 'actions',
    label: '操作',
  }
];

export default class RoleContent extends Component {
  static template = html`
  <template>
      <div class="content-header">
          <s-button skin="primary" on-click="onCreateRole" disabled="{{isLoading}}">
              <s-plus-1 class="button-icon mr4" is-button="{{false}}" />新建角色
          </s-button>
          <s-search placeholder="请输入角色名搜索" on-search="onSearch" on-input="onInputSearch" clearable></s-search>
      </div>
      <s-table loading="{{table.loading}}" 
        columns="{{roleColumns}}" 
        datasource="{{roleList}}" 
        on-filter="onFilterData">
          <div slot="c-name">
            <s-button skin="stringfy" on-click="goToRoleDetail(row)">{{row.name}}</s-button>
          </div>
          <ellipsis-tip
              slot="c-users"
              content="{{row.users | usersDesc}}"
              text="{{row.users | usersDesc}}"
              placement="top"
              showTip="{{true}}"
              alwaysTip="{{false}}"
              copy="{{false}}"
          >
          </ellipsis-tip>
          <ellipsis-tip
              slot="c-comment"
              content="{{row.comment || '-'}}"
              text="{{row.comment || '-'}}"
              placement="top"
              showTip="{{true}}"
              alwaysTip="{{false}}"
              copy="{{false}}"
          >
          </ellipsis-tip>
          <div slot="c-actions">
              <template>
                <s-tooltip s-if="editDiabled" content="当前集群状态不支持编辑角色">
                  <s-button
                    disabled="{{editDiabled}}"
                    skin="stringfy"
                  >
                      编辑
                  </s-button>
                </s-tooltip>
                <s-button
                  s-else
                  skin="stringfy"
                  on-click="onEditRole(row, rowIndex)"
                >
                  编辑
                </s-button>
              </template>
              <s-button
                  skin="stringfy"
                  on-click="onDeleteRole(row, rowIndex)"
              >
                  删除
              </s-button>
          </div>
      </s-table>
      <div class="pagination">
        <s-pagination
          slot="pager"
          layout="{{'pageSize, pager, go'}}"
          total="{{pager.totalCount}}"
          page="{{pager.pageNo}}"
          page-size="{{pager.pageSize}}"
          on-pagerChange="onPageChange"
          on-pagerSizeChange="onPageSizeChange"
        >
        </s-pagination>
      </div>
      <s-role-dailog 
        s-if="showDialog" 
        deployId="{{deployId}}" 
        version="{{version}}" 
        rowData="{{curRowData}}" 
        on-close="closeDialog">
      </s-role-dailog>
    </template>
    `;

  static components = {
    's-table': Table,
    's-button': Button,
    's-search': Search,
    's-plus-1': Plus1,
    's-pagination': Pagination,
    'ellipsis-tip': EllipsisTip,
    's-role-dailog': RoleDialog,
    's-tooltip': Tooltip,
  };

  static computed = {
    editDiabled() {
      const version = this.data.get('version');
      const versionTemp: any = version.split('.');
      if (versionTemp[0] < 2) {
        return true;
      } else if (versionTemp[0] == 2) {
        return versionTemp[1] >= 1 ? false : true;
      }
      return false;
    }
  }

  static filters = {
    usersDesc(data: string[]): string {
      return data && data.length > 0 ? data.join(', ') : '-';
    }
  }

  /**
  * 初始化数据
  *
  * @returns 返回初始化的数据对象，包含分页信息、用户列和用户列表
  */
  initData() {
    return {
      pager: {
        pageSize: 10,
        pageNo: 1,
        totalCount: 0
      },
      roleColumns: roleColumns,
      roleList: [],
      showDialog: false,
      curRowData: {},
    }
  }

  /**
    * 获取授权角色列表
    *
    * @returns 返回授权用户列表和分页信息
    */
  @debounce(500)
  async getAuthRoleList() {
    const {deployId, origin, searchValue} = this.data.get("");
    if (!deployId) {
      Notification.error('deployId 参数缺失');
      return;
    }
    const params = {
      deployId: deployId,
      pageNo: this.data.get('pager.pageNo'),
      pageSize: this.data.get('pager.pageSize'),
      ...origin ? {origin} : {},
      ...searchValue ? {
        searchKey: 'roleName',
        searchValue: searchValue
      } : {}
    };

    this.data.set('table.loading', true);
    try {
      const res = await this.$http.paloPost('engineAuthRoleList', params);
      this.data.set('roleList', res?.roles || []);
      this.data.set('pager.totalCount', res?.total || 0);
      this.data.set('table.loading', false);
    }
    catch (e) {
      this.data.set('table.loading', false);
    }
  }

  /**
  * 当组件附加到 DOM 时调用此方法。
  *
  * @returns 返回一个 Promise，当获取授权用户列表的操作完成时解析。
  */
  async attached() {
    if (this.data.get('isLoading')) {
      this.data.set('table.loading', true);
      this.watch('isLoading', async () => {
        await this.getAuthRoleList();
      });
    } else {
      await this.getAuthRoleList();
    }
  }

  /**
  * 页面改变时触发的方法
  *
  * @param target 包含页码和页面大小的对象
  */
  onPageChange(target: {value: {page: number; pageSize: number}}) {
    this.data.set('pager.pageNo', target.value.page);
    this.getAuthRoleList();
  }

  /**
  * 当分页大小改变时调用的方法
  *
  * @param target 包含分页信息的对象
  */
  onPageSizeChange(target: {value: {page: number; pageSize: number}}) {
    this.data.set('pager', {pageNo: 1, pageSize: target.value.pageSize});
    this.getAuthRoleList();
  }

  /**
  * 创建角色时触发的回调方法
  */
  onCreateRole() {
    this.data.set('curRowData', {})
    this.data.set('showDialog', true);
  }
  /**
  * 编辑角色信息
  */
  onEditRole(rowData: any) {
    this.data.set('curRowData', rowData)
    this.data.set('showDialog', true);
  }
  /**
  * 删除角色时触发的函数
  */
  async onDeleteRole(rowData: any) {
    const {name} = rowData;
    const deployId = this.data.get("deployId");
    if (!deployId || !name) {
      Notification.error('请求必填参数缺失');
      return;
    }
    await Dialog.confirm({
      content: `"${name}"删除后无法恢复，请确认是否删除？`,
      title: '删除',
      onOk: async () => {
        try {
          await this.$http.paloPost('engineAuthRoleDelete', {
            deployId,
            roleName: name,
          });
          Notification.success('删除成功');
          await this.getAuthRoleList();
        } catch (e) {
          Notification.error('删除失败，请重新尝试');
        }
      }
    });
  }
  /**
  * 跳转到角色详情页面
  */
  goToRoleDetail(rowData: any) {
    const {name} = rowData;
      // 这里更新路由对象中的 query
      const query = serializeQueryEncode({
        ...this.data.get('query'),
        type: 'roledetail',
        roleName: name,
      });
      redirect(`#/palo/detail?${query}`);
  }
  /**
  * 过滤数据的异步方法
  *
  * @param event 包含字段和过滤条件的事件对象
  * @param event.field 字段对象
  * @param event.filter 过滤条件对象
  */
  async onFilterData(event: {field: any; filter: any}) {
    const {field, filter} = event;
    this.data.set(`${field.name}`, filter.value);
    await this.getAuthRoleList();
  }

  /**
  * 处理输入框搜索事件
  *
  * @param event 事件对象，包含搜索值
  */
  onInputSearch(event: {value: string}) {
    this.data.set('searchValue', event.value);
  }
  /**
  * 搜索角色
  *
  * @returns 无返回值
  */
  async onSearch() {
    this.data.set('pager', {pageNo: 1, pageSize: 10});
    await this.getAuthRoleList();
  }

  async closeDialog(e: {tag: boolean}) {
    this.data.set('showDialog', false);
    e.tag && await this.getAuthRoleList();
  }
}
