import {Component} from "san"
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Alert, Notification} from '@baidu/sui'
import {PasswordInput} from '@components/password-input';
import {passwordValidator} from '@common/utils/rules';
import {passwordCrypt, base64Encode} from '@common/utils/index';
import './index.less';
export default class ResetPwdDialog extends Component {
  static template = html`
  <template>
    <s-dialog
      class="dialog-container"
      width="600"
      title="重置密码"
      open="{= open =}"
      on-confirm="confirmContent"
      on-close="onClose(false)"
      confirming="{{confirmLoading}}"
      widthStrictly
    >
      <s-alert skin="warning" showIcon="{{false}}" style="margin-bottom: 24px">
          重置密码可能会导致该账号的连接中断
      </s-alert>
      <s-form s-ref="restPwdForm" data="{=formData=}" rules="{{rules}}" label-align="left">
        <s-form-item
          prop="password" 
          label="密码:" 
          help="支持字母、数字、特殊字符，至少包含其中三种，长度8~16个字符"
        >
          <password-input
              width="390"
              value="{= formData.password =}"
          />
        </s-form-item>
        <s-form-item
            label="确认密码:"
            prop="confirmPassword">
            <password-input
                width="390"
                value="{= formData.confirmPassword =}"
            />
        </s-form-item>
      </s-form>
    </s-dialog>
  </template>
  `

  static components = {
    's-dialog': Dialog,
    's-form': Form,
    's-form-item': Form.Item,
    'password-input': PasswordInput,
    's-alert': Alert
  }

  initData() {
    return {
      open: true,
      formData: {
        password: '',
        confirmPassword: '',
      },
      rules: {
        password: [
          {required: true, message: '请输入密码'},
          {
            validator: passwordValidator
          }
        ],
        confirmPassword: [
          {required: true, message: '请输入密码'}, {
            validator: (rule: any, value: any, callback: (arg0?: string | undefined) => void) => {
              const newPassword = this.data.get('formData.password');
              if (value !== newPassword) {
                return callback('两次输入的密码不一致');
              }
              callback();
            }
          }
        ],
      },
      confirmLoading: false,
    }
  }

  onClose(tag = false) {
    this.fire('close', {tag})
  }

  async confirmContent() {
    try {
      this.data.set('confirmLoading', true);
      await this.ref('restPwdForm').validateFields();

      const {deployId, rowData, formData} = this.data.get('');
      if (!deployId || !rowData || !formData.password) {
        Notification.success('参数缺失');
        return;
      }
      const cryptPwd: any = passwordCrypt({cryptCont: formData.password});
      const params = {
        deployId,
        userName: rowData.user,
        host: rowData.host,
        password: base64Encode(cryptPwd)
      };
      await this.$http.paloPost('engineAuthUserRestPwd', params);
      Notification.success('密码重置成功');
      this.onClose(true);
    }
    catch (err) {
      Notification.success('密码重置失败');
      console.error('reset pwd:', err);
    }
    finally {
      this.data.set('confirmLoading', false);
    }
  }
}