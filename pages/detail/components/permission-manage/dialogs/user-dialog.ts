/*
 * @Author: hong<PERSON><EMAIL>
 * @Date: 2025-09-18 15:21:24
 * @LastEditTime: 2025-09-23 11:50:26
 */
import {Component} from "san"
import {html} from '@baiducloud/runtime';
import {Dialog, Form, Input, Radio, InputNumber, Select, Tooltip, Notification} from '@baidu/sui'
import {PasswordInput} from '@components/password-input';
import './index.less';
import DrapShow from '../components/drap-show';
import {passwordValidator} from '@common/utils/rules';
import {passwordCrypt, base64Encode} from '@common/utils/index';

export default class UserDialog extends Component {
  static template = html`
  <template>
    <s-dialog
      open="{{open}}"
      class="dialog-container"
      width="600"
      title="{{mode[dialogMode]}}用户"
      on-confirm="confirmContent"
      on-close="onClose(false)"
      confirming="{{confirmLoading}}"
      widthStrictly
    >
      <s-form  s-ref="user-form" data="{=formData=}" rules="{{rules}}" label-align="left">
        <div class="base-content">
          <div class="title-info">基本信息</div> 
          <s-form-item 
            prop="userName" 
            label="用户名:"
            help="以字母开头，支持字母、数字、下划线, 长度1~64个字符"
          >
            <s-input 
              disabled="{= dialogMode === 'edit' ? true : false =}" 
              value="{=formData.userName=}"  
              width="400"
              placeholder="请输入用户名" 
            >
            </s-input>
          </s-form-item>
          <s-form-item prop="host" label="主机:">
            <s-input value="{=formData.host=}" width="400"  
              disabled="{= dialogMode === 'edit' ? true : false =}"
            ></s-input>
          </s-form-item>
          <s-form-item 
            s-if="dialogMode !== 'edit'" 
            prop="password" 
            label="密码:" 
            help="支持字母、数字、特殊字符，至少包含其中三种，长度8~16个字符"
          >
            <password-input
                width="390"
                value="{= formData.password =}"
            />
          </s-form-item>
          <s-form-item
              s-if="dialogMode !== 'edit'" 
              label="确认密码:"
              prop="confirmPassword">
              <password-input
                  width="390"
                  value="{= formData.confirmPassword =}"
              />
          </s-form-item>
          <s-form-item
              s-if="showComment"
              label="描述:"
              prop="comment">
              <s-textarea  width="405" height="80" placeholder="请输入描述信息" value="{=formData.comment=}" ></s-textarea>
          </s-form-item> 
        </div>
        <div class="safe-content">
          <div class="title-info">
            <div style="margin-right: 5px">安全策略</div>
            <s-drap-show on-change="changeDirect" size="25"/>
          </div>
          <div style="display:{{showSafeConten ? 'block' : 'none'}}; width: 100%;" class="safe-info">
            <s-form-item
                label="密码有效期:"
                prop="passwordExpire">
                <div class="group-input-common">
                  <s-radio-group
                      class="s-doc-radio"
                      datasource="{{datasource}}"
                      value="{=formData.passwordExpireTag=}"
                      enhanced="{{true}}"
                      radioType="button"
                      name="passwordExpireTag"
                  ></s-radio-group>
                  <s-tooltip content="调整区间：180~365">
                    <s-inputnumber
                        value="{=formData.passwordExpire=}"
                        min="{{180}}"
                        max="{{365}}"
                        class="group-input-common_input"
                    />天
                  </s-tooltip>
                </div>
            </s-form-item> 
            <s-form-item
                label="禁止历史密码:"
                prop="passwordHistory">
                <div class="group-input-common">
                  <s-radio-group
                      class="s-doc-radio"
                      datasource="{{datasource}}"
                      value="{=formData.passwordHistoryTag=}"
                      enhanced="{{true}}"
                      radioType="button"
                      name="passwordHistoryTag"
                  ></s-radio-group>
                  <s-tooltip content="调整区间：3~1000">
                    <s-inputnumber
                        value="{=formData.passwordHistory=}"
                        min="{{3}}"
                        max="{{1000}}"
                        class="group-input-common_input"
                    />个
                  </s-tooltip>
                </div>
            </s-form-item>
            <s-form-item
                label="密码错误锁定:"
                prop="failedLoginAttempts">
                <div class="group-input-common">
                  <s-radio-group
                      class="s-doc-radio"
                      datasource="{{datasource}}"
                      value="{=formData.failedLoginAttemptsTag=}"
                      enhanced="{{true}}"
                      radioType="button"
                      name="failedLoginAttemptsTag"
                      on-change="failedLoginAttemptsChange"
                  ></s-radio-group>
                  <s-tooltip content="调整区间：5~100">
                    <s-inputnumber
                        value="{=formData.failedLoginAttempts=}"
                        min="{{5}}"
                        max="{{100}}"
                        class="group-input-common_input"
                    />次
                  </s-tooltip>
                </div>
            </s-form-item> 
            <s-form-item s-if="formData.failedLoginAttemptsTag === 2"
                label="锁定时长:"
                prop="passwordLockTime">
                <s-tooltip content="调整区间：1~365">
                  <s-inputnumber
                      value="{=formData.passwordLockTime=}"
                      min="{{1}}"
                      max="{{365}}"
                  />
                </s-tooltip>
                <s-select
                    width="100"
                    size="small"
                    value="{=formData.passwordLockTimeUnit=}"
                    datasource="{{timeUnitList}}"
                    on-change="timeUnitChange"
                />
            </s-form-item>
            </div>
        </div>
      </s-form>
    </s-dialog>
  </template>
  `

  static components = {
    's-dialog': Dialog,
    's-form': Form,
    's-input': Input,
    's-form-item': Form.Item,
    'password-input': PasswordInput,
    's-textarea': Input.TextArea,
    's-radio': Radio,
    's-radio-group': Radio.RadioGroup,
    's-inputnumber': InputNumber,
    's-select': Select,
    's-drap-show': DrapShow,
    's-tooltip': Tooltip,
  }

  initData() {
    return {
      open: true,
      mode: {
        create: '创建',
        edit: '编辑'
      },
      showSafeConten: false,
      formData: {
        userName: '',
        host: '%',
        password: '',
        confirmPassword: '',
        comment: '',
        passwordExpireTag: 1,
        passwordExpire: 180,
        passwordHistoryTag: 1,
        passwordHistory: 3,
        failedLoginAttemptsTag: 1,
        failedLoginAttempts: 5,
        passwordLockTime: 1,
        passwordLockTimeUnit: 'DAY',
      },
      timeUnitList: [
        {label: '天', value: 'DAY'},
        {label: '小时', value: 'HOUR'},
        {label: '秒', value: 'SECOND'}
      ],
      datasource: [
        {label: '不限制', value: 1},
        {label: '自定义', value: 2}
      ],
      rules: {
        userName: [
          {required: true, message: '请输入用户名'},
          {
            validator(_: any, value: any, callback: any) {
              let pattern = /^[a-zA-Z][\w\-\_\/\.]{0,64}$/;
              if (!pattern.test(value)) {
                return callback('长度限制为1-65个字符，以字母开头，只允许包含字母、数字及 - _ . /');
              }
              callback();
            }
          }
        ],
        host: [{required: true, message: '请输入主机'}],
        password: [
          {required: true, message: '请输入密码'},
          {
            validator: passwordValidator
          }
        ],
        confirmPassword: [
          {required: true, message: '请输入密码'}, {
            validator: (rule: any, value: any, callback: (arg0?: string | undefined) => void) => {
              const newPassword = this.data.get('formData.password');
              if (value !== newPassword) {
                return callback('两次输入的密码不一致');
              }
              callback();
            }
          }
        ],
        passwordExpire: [{required: true, message: '请选择'}],
        passwordHistory: [{required: true, message: '请选择'}],
        failedLoginAttempts: [{required: true, message: '请选择'}],
        passwordLockTime: [{required: true, message: '请输入'}]
      },
      confirmLoading: false,
    }
  }

  static computed = {
    showComment() {
      const version = this.data.get('version');
      const versionTemp: any = version.split('.');
      if (versionTemp[0] < 2) {
        return false;
      } else if (versionTemp[0] == 2) {
        return versionTemp[1] >= 1 ? true : false;
      }
      return true;
    },
    dialogMode() {
      const rowData: any = this.data.get('rowData');
      return Object.keys(rowData).length ? 'edit' : 'create';
    }
  }

  inited() {
    const rowData = this.data.get('rowData');
    if (Object.keys(rowData).length <= 0) {
      return;
    }
    const formData = this.data.get('formData');
    // 安全策略的内容兼容, 多单选框进行初始化设置。
    const {user, host, comment, mysqlUserTableInfo} = rowData;
    const {
      passwordExpire, passwordHistory, failedLoginAttempts,
      passwordLockTime, passwordLockTimeUnit
    } = mysqlUserTableInfo ? mysqlUserTableInfo : {};
    const dataTemp = {
      userName: user,
      host: host,
      comment: comment,
      ...!passwordExpire ? {} : {
        passwordExpireTag: 2,
        passwordExpireUnit: 'DAY',
        passwordExpire: 190,
      },
      ...!passwordHistory ? {} : {
        passwordHistoryTag: 2,
        passwordHistory,
      },
      ...!failedLoginAttempts ? {} : {
        failedLoginAttemptsTag: 2,
        failedLoginAttempts,
        passwordLockTime,
        passwordLockTimeUnit
      }
    }
    this.data.set('formData', {...formData, ...dataTemp});
  }

  onClose(tag = false) {
    this.fire('close', {tag})
  }

  changeDirect(e) {
    this.data.set('showSafeConten', e === 'up' ? true : false)
  }

  failedLoginAttemptsChange(e) {
    this.data.set('formData.failedLoginAttemptsTag', e.value)
  }

  async confirmContent() {
    try {
      this.data.set('confirmLoading', true);
      await this.ref('user-form').validateFields();

      const {deployId, formData} = this.data.get('');
      if (this.data.get('dialogMode') === 'create') {
        await this.createUserContent(deployId, formData);
        Notification.success('创建成功');
      } else {
        await this.editUserContent(deployId, formData);
        Notification.success('编辑成功');
      }
      this.onClose(true);
    }
    catch (err) {
      console.error('confirmContent', err);
    }
    finally {
      this.data.set('confirmLoading', false);
    }
  }

  async createUserContent(deployId: any, formData: any) {
    const {
      userName, host, password, comment,
      passwordExpireTag, passwordExpire,
      passwordHistoryTag, passwordHistory,
      failedLoginAttemptsTag, failedLoginAttempts,
      passwordLockTime, passwordLockTimeUnit
    } = formData;
    const cryptPwd: any = passwordCrypt({cryptCont: password});
    const params = {
      deployId,
      userName,
      host,
      password: base64Encode(cryptPwd),
      ...comment ? {comment} : {},
      ...passwordExpireTag === 1 ? {} : {passwordExpire, passwordExpireUnit: 'DAY'},
      ...passwordHistoryTag === 1 ? {} : {passwordHistory},
      ...failedLoginAttemptsTag === 1 ? {} : {failedLoginAttempts, passwordLockTime, passwordLockTimeUnit},
    };
    await this.$http.paloPost('engineAuthUserCreate', params);
  }

  async editUserContent(deployId: any, formData: any) {
    const {
      userName, host, comment,
      passwordExpireTag, passwordExpire,
      passwordHistoryTag, passwordHistory,
      failedLoginAttemptsTag, failedLoginAttempts,
      passwordLockTime, passwordLockTimeUnit
    } = formData;
    const params = {
      deployId,
      userName,
      host,
      ...comment ? {comment} : {},
      ...passwordExpireTag === 1 ? {} : {passwordExpire, passwordExpireUnit: 'DAY'},
      ...passwordHistoryTag === 1 ? {} : {passwordHistory},
      ...failedLoginAttemptsTag === 1 ? {} : {failedLoginAttempts, passwordLockTime, passwordLockTimeUnit},
    };
    await this.$http.paloPost('engineAuthUserEdit', params);
  }
}