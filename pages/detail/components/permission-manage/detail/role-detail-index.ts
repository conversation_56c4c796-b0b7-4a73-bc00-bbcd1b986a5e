/**
 * 权限管理 - 角色详情
 * <AUTHOR>
 */
import {decorators, html, redirect, ServiceFactory} from '@baiducloud/runtime';
import {Button, Tabs, Notification, Dialog} from '@baidu/sui';
import {AppDetailPage, AppTabPage, AppDetailCell} from '@baidu/sui-biz';
import { serializeQueryEncode } from '@common/utils';
import {OutlinedLeft} from '@baidu/sui-icon';

// 授予用户详情页
import GrantingUsersAndRolesCom from '../components/granting-users-and-roles';
// 数据权限详情页
import DataPermissionCom from '../data-permission-tab/index-data-permission';
// 资源权限详情页
import ResourcePermissionCom from '../components/resource-permission';

const klass = 'permission-detail';

const template = html`
    <template>
        <app-detail-page class="${klass}" pageTitle="{{'角色管理'}}" backTo="{{backTo}}">
            <div slot="pageTitle" class="${klass}-instanceinfo">
                <div class="${klass}-left">
                    <span class="back-btn" on-click="onBack">
                        <outlined-left width="{{16}}" />
                    </span>
                    <h4 class="${klass}-instancename">{{query.roleName + ' 角色管理'}}</h4>
                </div>
                <div class="${klass}-right">
                    <div class="${klass}-button">
                        <s-button on-click="operateRole('edit')" class="mr8">编辑</s-button>
                        <s-button on-click="operateRole('delete')" class="mr8">删除</s-button>
                    </div>
                </div>
            </div>
            <div class="${klass}-baseinfo">
                <app-detail-cell datasource="{{baseInfoDatasource}}" divide="3" labelWidth="120px" />
            </div>
            <s-tabs active="{{operateTabValue}}" on-change="onOperateTabChange">
                <s-tab-pane label="授予用户" key="{{0}}">
                    <template s-if="operateTabValue === 0 ">
                        <granting-users-and-roles-com query="{{query}}" detail="{{detailData}}" />
                    </template>
                </s-tab-pane>
                <s-tab-pane label="数据权限" key="{{1}}">
                    <template s-if="operateTabValue === 1">
                        <data-permission-com query="{{query}}" />
                    </template>
                </s-tab-pane>
                <s-tab-pane label="资源权限" key="{{2}}">
                    <template s-if="operateTabValue === 2">
                        <resource-permission-com query="{{query}}" detail="{{detailData}}" />
                    </template>
                </s-tab-pane>
            </s-tabs>
        </app-detail-page>
    </template>
`;

export default class PaloDetail extends AppDetailPage {
    static template = template;
    static components = {
        's-button': Button,
        's-tabs': Tabs,
        's-tab-pane': Tabs.TabPane,
        'app-detail-page': AppDetailPage,
        'app-detail-cell': AppDetailCell,
        'app-tab-page': AppTabPage,
        'app-tab-page-panel': AppTabPage.TabPane,
        'granting-users-and-roles-com': GrantingUsersAndRolesCom,
        'data-permission-com': DataPermissionCom,
        'resource-permission-com': ResourcePermissionCom,
        'outlined-left': OutlinedLeft,
    };

    static computed = {
        backTo() {
            const query = this.data.get('query');
            const {deployId, page = 5, version} = query;
            const queryParams = serializeQueryEncode({
                deployId,
                page,
                version,
                type: 'rolelist'
            });
            return {
                text: '返回',
                url: `#/palo/detail?${queryParams}`
            };
        },
        baseInfoDatasource(): {label: string; value: string}[] {
            return [
                {
                    label: '描述：',
                    value: '-'
                }
            ]
        },
    };

    initData() {
        return {
            operateTabValue: 0, // 详情页 tab, 默认 0 为「授予角色」
            loading: false,
            detailData: {},
            showDialog: false,
            showResetPwdDialog: false,
        };
    }

    async attached() {
        console.log('role-detail-index, attached==', this.data.get('query'));
        await this.getClusterDetail();
    }
    // 获取集群详情
    async getClusterDetail() {
        const {deployId, roleName} = this.data.get('query');
        if (!deployId) {
            Notification.error('缺少集群ID，请返回列表页重试');
            return;
        }
        const params = {
            deployId,
            roleName,
        };
        this.data.set('loading', true);

        // TODO: 角色详情
        this.data.set('loading', false);
    }

    // 操作「编辑」or 「删除」
    operateRole(type: 'edit' | 'delete') {
        console.log('type==', type);
        if (type === 'edit') {
            this.data.set('showDialog', true);
        } else {
            this.onDeleteRole(this.data.get('editAndRestUserData'));
        }
    }

    // 详情页tab切换
    onOperateTabChange(target: {value: {key: string}}) {
        this.data.set('operateTabValue', target.value.key);
    }

    // 返回
    onBack() {
        const backTo = this.data.get('backTo');
        redirect(backTo.url);
    }

    /**
     * 删除角色时触发的函数
     */
    async onDeleteRole(rowData: any) {
        const {roleName} = rowData;
        const {deployId} = this.data.get("query");
        if (!deployId || !roleName) {
            Notification.error('请求必填参数缺失');
            return;
        }
        await Dialog.confirm({
            content: `"${rowData.roleName}"删除后无法恢复，请确认是否删除？`,
            title: '删除',
            onOk: async () => {
                try {
                    await this.$http.paloPost('engineAuthRoleDelete', {
                        deployId,
                        roleName,
                    });
                    Notification.success('删除成功');
                    await this.getAuthUserList();
                } catch (e) {
                    Notification.error('删除失败，请重新尝试');
                }
            }
        });
    }

    async closeDialog() {
        this.data.set('showDialog', false);
    }

    closeResetPwdDialog() {
        this.data.set('showResetPwdDialog', false);
    }
}
