import {Component, DataTypes} from 'san';
import {html} from '@baiducloud/runtime';
import {Table, Button, Radio} from '@baidu/sui';
import {grantType} from '../../types';
import RowAndColumnDrawer from './row-and-column-drawer';
import {SanComputedProps} from 'types/global';
import PrivilegeTags from '../../components/privilege-tags';

const klass = 'meta-r-content-com';

export default class GrantingRolesCom extends Component {
    static template = html`
        <div class="${klass}">
            <div style="margin-bottom: 16px">
                <s-radio-group
                    class="s-doc-radio"
                    value="{=radioGroupValue=}"
                    radioType="button"
                    name="radioGroupValue"
                >
                    <s-radio label="全部" value="all" />
                    <s-radio label="继承权限" value="inherit" />
                    <s-radio label="直接授权" value="direct" />
                </s-radio-group>
            </div>
            <s-table columns="{{columns}}" datasource="{{displayTableData}}">
                <div slot="c-label" class="${klass}-cell-text">
                    {{row.label}}
                </div>
                <div slot="c-privilegeSources">
                   <privilege-tags
                        datasource="{{row.privileges}}"
                        editable="{{true}}"
                        type="${grantType.DATA}"
                        isUser="{{isUser}}"
                        on-edit="onEditPrivileges(row)"
                        on-confirmEdit="onConfirmEditPrivileges"
                    />
                </div>
                <div slot="c-actions">
                    <s-button
                        s-if="row.treeLevel === 4"
                        skin="stringfy"
                        on-click="handleManageColumnPrivilege($event, row)"
                    >
                        管理行列权限
                    </s-button>
                    <span s-else>-</span>
                </div>
            </s-table>
        </div>
    `;

    static DataTypes = {
        // 路由参数 route.query
        query: DataTypes.object,
        // 左侧面板Tree 选中数据
        treeSelectData: DataTypes.arrayOf(
            DataTypes.shape({
                treeLevel: DataTypes.number,
                key: DataTypes.string,
                label: DataTypes.string,
                privilegeSources: DataTypes.array
            })
        ),
        // 针对 用户详情和角色详情接口参数不同
        baseAPIParams: DataTypes.object,
        // 是否为用户详情页 (角色详情页为 false)
        isUser: DataTypes.bool
    };

    static components = {
        's-table': Table,
        's-radio': Radio,
        's-radio-group': Radio.RadioGroup,
        's-button': Button,
        'privilege-tags': PrivilegeTags,
    };

    static computed: SanComputedProps = {
        // 表格数据，根据 radioGroupValue 进行过滤
        displayTableData() {
            const treeSelectData = this.data.get('treeSelectData');
            const radioGroupValue = this.data.get('radioGroupValue');
            // 仅过滤 item.privilegeSources 中的节点内容，并转换数据格式以适配 privilege-tags 组件
            return treeSelectData.map((item: any) => ({
                ...item,
                // 保留原始的 privilegeSources 字段
                privilegeSources: item.privilegeSources?.filter((privilege: any) => {
                    if (radioGroupValue === 'all') {
                        return true;
                    } else if (radioGroupValue === 'inherit') {
                        return !privilege.direct;
                    } else if (radioGroupValue === 'direct') {
                        return privilege.direct;
                    }
                    return false;
                }),
                // 添加 privileges 字段，转换数据格式以适配 privilege-tags 组件
                privileges: item.privilegeSources?.filter((privilege: any) => {
                    if (radioGroupValue === 'all') {
                        return true;
                    } else if (radioGroupValue === 'inherit') {
                        return !privilege.direct;
                    } else if (radioGroupValue === 'direct') {
                        return privilege.direct;
                    }
                    return false;
                }) || []
            }));
        }
    };

    initData() {
        return {
            radioGroupValue: 'all',
            treeSelectData: [],
            columns: [
                {
                    name: 'label',
                    label: '名称'
                },
                {
                    name: 'privilegeSources',
                    label: '权限点'
                },
                {
                    name: 'actions',
                    label: '操作'
                }
            ]
        };
    }

    // 管理行列权限
    handleManageColumnPrivilege(event: Event, row: any) {
        event.stopPropagation();
        const Drawer = new RowAndColumnDrawer({
            data: {
                deployId: this.data.get('query').deployId,
                row,
                baseAPIParams: this.data.get('baseAPIParams'),
                isUser: this.data.get('isUser')
            }
        });
        Drawer.attach(document.body);
    }

    // 编辑权限点
    onEditPrivileges(row: any) {
        console.log('编辑权限点 row: ', row);
    }

    // 确认编辑权限点
    onConfirmEditPrivileges(data: any) {
        console.log('确认编辑权限点 data: ', data);
        // TODO: 调用接口保存权限点变更
        // 这里可以参考 resource-permission.ts 中的实现
        // 需要根据实际的 API 接口进行调用
    }
}
