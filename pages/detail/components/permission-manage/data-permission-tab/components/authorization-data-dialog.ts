import {Component} from 'san';
import _ from 'lodash';
import {html} from '@baiducloud/runtime';
import {
    Dialog,
    Button,
    Radio,
    Search,
    Form,
    Select,
    Input
} from '@baidu/sui';
import {authObject, tablePermissionType} from '../../types';

const klass = 'auth-data-dialog';

export default class AuthDataDialog extends Component{
    static template = html`
    <template>
        <s-dialog
            title="授权数据"
            open="{=open=}"
            mask="{{true}}"
            class="${klass}"
            height="350"
            width="800"
            widthStrictly
            on-close="onClose">
            <div>
                <s-form
                    s-ref="auth-data-dialog-form"
                    data="{=selected=}"
                    rules="{{rules}}"
                    label-align="left"
                >
                    <s-form-item label="授权对象：">
                        <s-radio-group
                            datasource="{{authObject}}"
                            value="{=selectedAuthObject=}"
                            enhanced
                            radioType="button"
                        >
                        </s-radio-group>
                    </s-form-item>

                    <s-form-item label="表权限：" s-if="showTablePermissionTypeSelect">
                        <s-radio-group
                            datasource="{{tablePermissionType}}"
                            value="{=formData.selectedTablePermissionType=}"
                            enhanced
                            radioType="button"
                            on-change="handleChangeTablePermissionType"
                        >
                        </s-radio-group>
                    </s-form-item>

                    <s-form-item label="选择对象：">
                        <!--global-->
                        <s-select
                            s-if="showGlobalSelect"
                            value="已选全局数据"
                            disabled
                        />

                        <!--catalog-->
                        <s-select
                            s-if="showCatalogSelect"
                            multiple="{{isCatalogMultiple}}"
                            taggable
                            checkAll
                            filterable
                            datasource="{{catalogList}}"
                            value="{=formData.selectedCatalog=}"
                            placeholder="请选择目录"
                            on-change="handleChangeCatalog"
                        />

                        <!--database-->
                        <s-select
                            s-if="showDatabaseSelect"
                            multiple="{{isDatabaseMultiple}}"
                            taggable
                            checkAll
                            filterable
                            datasource="{{databaseList}}"
                            value="{=formData.selectedDatabase=}"
                            placeholder="请选择数据库"
                            on-change="handleChangeDatabase"
                        />
                        
                        <!--table-->
                        <s-select
                            s-if="showTableSelect"
                            multiple="{{isTableMultiple}}"
                            taggable
                            checkAll
                            filterable
                            datasource="{{tableList}}"
                            value="{=formData.selectedTable=}"
                            placeholder="请选择表"
                            on-change="handleChangeTable"
                        />
                        
                        <!--column-->
                        <s-select
                            s-if="showColumnSelect"
                            multiple
                            taggable
                            checkAll
                            filterable
                            datasource="{{columnList}}"
                            value="{=formData.selectedColumn=}"
                            placeholder="请选择列字段"
                            on-change="handleChangeColumn"
                        />
                    </s-form-item>

                    <s-form-item label="权限点：" s-if="showPrivilegeSelect">
                        <s-select
                            multiple
                            taggable
                            checkAll
                            datasource="{{privilegeList}}"
                            value="{=formData.selectedPrivilege=}"
                            placeholder="请选择权限点"
                            on-change="handleChangePrivilege"
                        />
                    </s-form-item>

                    <s-form-item label="行过滤规则：" s-if="showRowFilterRule">
                        <s-textarea
                            value="{=formData.rowFilterRule=}"
                            width="{{652}}"
                            height="{{80}}"
                            placeholder="请输入行过滤规则，例如：region = 'East' 或 dept_id IN (1, 2, 3)。支持使用 AND、OR组合多个规则"
                            on-change="handleChangeRowFilterRule"
                        />
                    </s-form-item>
                </s-form>
            </div>
            <div slot="footer">
                <s-button on-click="onClose">取消</s-button>
                <s-button on-click="onConfirm" skin="primary">确定</s-button>
            </div>
        </s-dialog>
    </template>`;

    static components = {
        's-dialog': Dialog,
        's-button': Button,
        's-radio-group': Radio.RadioGroup,
        's-search': Search,
        's-form': Form,
        's-form-item': Form.Item,
        's-select': Select,
        's-textarea': Input.TextArea,
    };

    static computed = {
        showGlobalSelect() {
            return this.data.get('selectedAuthObject') === authObject.GLOBAL;
        },
        showCatalogSelect() {
            return this.data.get('selectedAuthObject') !== authObject.GLOBAL;
        },
        showDatabaseSelect() {
            return (
                this.data.get('selectedAuthObject') !== authObject.GLOBAL
                && this.data.get('selectedAuthObject') !== authObject.CATALOG
            ) && !!this.data.get('formData.selectedCatalog');
        },
        showTableSelect() {
            return (
                this.data.get('selectedAuthObject') !== authObject.GLOBAL
                && this.data.get('selectedAuthObject') !== authObject.CATALOG
                && this.data.get('selectedAuthObject') !== authObject.DATABASE
            ) && !!this.data.get('formData.selectedDatabase');
        },
        showColumnSelect() {
            return this.data.get('showTableSelect')
            && this.data.get('formData.selectedTablePermissionType') === tablePermissionType.COLUMN
            && !!this.data.get('formData.selectedTable');
        },
        showPrivilegeSelect() {
            return this.data.get('formData.selectedTablePermissionType') !== tablePermissionType.ROW;
        },
        showRowFilterRule() {
            return this.data.get('selectedAuthObject') === tablePermissionType.TABLE
                && this.data.get('formData.selectedTablePermissionType') === tablePermissionType.ROW;
        },
        showTablePermissionTypeSelect() {
            return this.data.get('selectedAuthObject') === tablePermissionType.TABLE;
        },
        isCatalogMultiple() {
            return this.data.get('selectedAuthObject') === authObject.CATALOG;
        },
        isDatabaseMultiple() {
            return this.data.get('selectedAuthObject') === authObject.DATABASE;
        },
        isTableMultiple() {
            return (
                this.data.get('selectedAuthObject') === tablePermissionType.TABLE
                    && this.data.get('formData.selectedTablePermissionType') === tablePermissionType.TABLE
                || this.data.get('selectedAuthObject') === authObject.VIEW
                || this.data.get('selectedAuthObject') === authObject.MATERIALIZED_VIEW
            );
        }
    }

    initData() {
        return {
            open: true,
            rules: {

            },
            authObject: [
                {
                    label: '全局',
                    value: authObject.GLOBAL,
                },
                {
                    label: '目录',
                    value: authObject.CATALOG,
                },
                {
                    label: '数据库',
                    value: authObject.DATABASE,
                },
                {
                    label: '表',
                    value: authObject.TABLE,
                },
                {
                    label: '视图',
                    value: authObject.VIEW,
                },
                {
                    label: '物化视图',
                    value: authObject.MATERIALIZED_VIEW,
                }
            ],
            tablePermissionType: [
                {
                    label: '整表权限',
                    value: tablePermissionType.TABLE,
                },
                {
                    label: '列权限',
                    value: tablePermissionType.COLUMN,
                },
                {
                    label: '行权限',
                    value: tablePermissionType.ROW,
                },
            ],
            defaultSelected: {
                selectedCatalog: '',
                selectedDatabase: '',
                selectedTable: '',
                selectedColumn: [],
                selectedTablePermissionType: tablePermissionType.TABLE,
                selectedPrivilege: [],
                rowFilterRule: '',
            },
            formData: {},
            selectedAuthObject: authObject.GLOBAL,

            //test data
            catalogList: [{label: '目录1', value: 'catalog1'}, {label: '目录2', value: 'catalog2'}],
            databaseList: [{label: '数据库1', value: 'database1'}, {label: '数据库2', value: 'database2'}],
            tableList: [{label: '表1', value: 'table1'}, {label: '表2', value: 'table2'}],
            columnList: [{label: '列1', value: 'column1'}, {label: '列2', value: 'column2'}],
            privilegeList: [{label: '权限1', value: 'privilege1'}, {label: '权限2', value: 'privilege2'}],
        }
    }

    attached() {
        this.data.set('formData', _.cloneDeep(this.data.get('defaultSelected')));
        this.watch('formData', value => {
            console.log('formData', value);
        })
        this.setWatch();
    }

    setWatch() {
        // 切换授权对象，清空已选择
        this.watch('selectedAuthObject', value => {
            const initData = _.cloneDeep(this.data.get('defaultSelected'));
            if (value === authObject.CATALOG) {
                initData.selectedCatalog = [];
            }
            this.data.set('formData', initData);
        });

        this.watch('formData.selectedCatalog', value => {
            if (this.data.get('selectedAuthObject') === authObject.CATALOG) {
                return;
            }
            this.data.set('formData.selectedDatabase', '');
        })

        this.watch('formData.selectedDatabase', value => {
            if (this.data.get('selectedAuthObject') === authObject.DATABASE) {
                return;
            }
            this.data.set('formData.selectedTable', '');
        })

        this.watch('formData.selectedTable', value => {
            if (this.data.get('formData.selectedTablePermissionType') === tablePermissionType.TABLE) {
                return;
            }
            this.data.set('formData.selectedColumn', []);
        })
    }


    // 确认
    async onConfirm() {
        // TODO: 获取表单数据发送请求
        const formData = this.data.get('formData');
        console.log('formData', formData);
        
        return;
        // 向父组件告知刷新 tree 数据
        this.fire('success', {});
        this.onClose();
    }
    // 取消
    onClose() {
        this.data.set('open', false);
        this.dispose && this.dispose();
    }

    handleChangeTablePermissionType({value}) {
        this.data.set('formData.selectedTable', '');
    }
}