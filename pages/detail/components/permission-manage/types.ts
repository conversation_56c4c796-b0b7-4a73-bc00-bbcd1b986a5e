export enum userStatus {
    ACTIVE = 'ACTIVE',
    LOCK = 'LOCK'
  }
  
export const statusInfo = {
    [userStatus.ACTIVE]: '正常',
    [userStatus.LOCK]: '锁定'
}

export enum userOrigin {
    SYSTEM = 'SYSTEM',
    USER = 'USER'
}

export const originInfo = {
    [userOrigin.SYSTEM]: '系统',
    [userOrigin.USER]: '用户'
}

export enum grantType {
    DATA = 'data',
    RESOURCE = 'resource'
}

export enum authObject {
    GLOBAL = 'global',
    CATALOG = 'catalog',
    DATABASE = 'database',
    TABLE = 'table',
    VIEW = 'view',
    MATERIALIZED_VIEW = 'materializedView'
}

export enum tablePermissionType {
    TABLE = 'table',
    ROW = 'row',
    COLUMN = 'column'
}
